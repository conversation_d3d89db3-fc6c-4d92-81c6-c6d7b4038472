0000000000000000000000000000000000000000 a12ad7b224c1b0ffd501d9c0fbd76bfb64984d65 berkincetin <<EMAIL>> 1751526950 +0300	clone: from https://bitbucket.org/netaxtech-team/atlas-q-a-rag.git
a12ad7b224c1b0ffd501d9c0fbd76bfb64984d65 da9b53d68fb00e0c65cab8007e149b38ffa181bd berkincetin <<EMAIL>> 1751612376 +0300	commit: Added detailed logs
da9b53d68fb00e0c65cab8007e149b38ffa181bd a5d8db20b79c466f9af802adbf42aa42fea4e748 berkincetin <<EMAIL>> 1751613535 +0300	commit: requirements.backend-only restored
a5d8db20b79c466f9af802adbf42aa42fea4e748 3e9f1fe35ba512197ab3859be8af12739f3846ed berkincetin <<EMAIL>> 1751622629 +0300	commit: sql connection string changed from 10.1.1.83 to 10.1.1.144
3e9f1fe35ba512197ab3859be8af12739f3846ed 9a7b246098ec0962142e7e50f2b7d4306b99117b berkincetin <<EMAIL>> 1751622636 +0300	pull --tags origin main: Merge made by the 'ort' strategy.
9a7b246098ec0962142e7e50f2b7d4306b99117b 99be61fe91cfd767f61f830d03418cbb9774c7a1 berkincetin <<EMAIL>> 1751627100 +0300	commit: web search depth changed
99be61fe91cfd767f61f830d03418cbb9774c7a1 6ab5930d00e6c9be0f839cced346f81dc1f3276c berkincetin <<EMAIL>> 1751891813 +0300	commit: sql query tool fixed
6ab5930d00e6c9be0f839cced346f81dc1f3276c 80d528b5ebe6dc4aad0303d02dc25380283ba7bb berkincetin <<EMAIL>> 1751892218 +0300	commit: config files update
80d528b5ebe6dc4aad0303d02dc25380283ba7bb 21d6e051e2e1f311a01218ba5798394d7395a5d8 berkincetin <<EMAIL>> 1751894650 +0300	commit: document processing update
21d6e051e2e1f311a01218ba5798394d7395a5d8 23bdad107ca4631b1be09b358a8a66c39611592b berkincetin <<EMAIL>> 1751972159 +0300	commit: tool selection fixed
23bdad107ca4631b1be09b358a8a66c39611592b 72cfd0b6b34e10bc8d102b3ff13d68fe96159f25 berkincetin <<EMAIL>> 1751982888 +0300	commit: langchain_chroma library added
72cfd0b6b34e10bc8d102b3ff13d68fe96159f25 869392cfd3882d7e670d3525cd15b76080019079 berkincetin <<EMAIL>> 1751983064 +0300	commit: dockerfile-requirements file changed
869392cfd3882d7e670d3525cd15b76080019079 26da7e75457be1635add69e84edb36e498927f05 berkincetin <<EMAIL>> 1752217218 +0300	commit: sql query test function added
26da7e75457be1635add69e84edb36e498927f05 64e494d199988ddb42b2daba048a559968383278 berkincetin <<EMAIL>> 1752234086 +0300	commit: sql query test function update
64e494d199988ddb42b2daba048a559968383278 4596aad971eb529c5b5d3414756a8209cbc8f724 berkincetin <<EMAIL>> 1752237708 +0300	commit: missing libidoc package added
4596aad971eb529c5b5d3414756a8209cbc8f724 32dae4b3ba5292d09ef58b07136458f7097c8f9b berkincetin <<EMAIL>> 1752238240 +0300	commit: unixodbc added
32dae4b3ba5292d09ef58b07136458f7097c8f9b 678f6304390e32e08e4e7a05550c2fd435e90ab8 berkincetin <<EMAIL>> 1752239670 +0300	commit: ms sql odbc driver update
678f6304390e32e08e4e7a05550c2fd435e90ab8 c14de6ef54f52767334c14b42250a00e5fa1c708 berkincetin <<EMAIL>> 1752242093 +0300	commit: sql server initialized issue fixed
c14de6ef54f52767334c14b42250a00e5fa1c708 c807d8ccd17f4f3c252e376f04a2c11072c24145 berkincetin <<EMAIL>> 1752242812 +0300	commit: prompts upgrade
c807d8ccd17f4f3c252e376f04a2c11072c24145 858035fb26f0c3776abb8176540b827af10af16c berkincetin <<EMAIL>> 1752244140 +0300	commit: priority_domain: atlas.edu.tr added
858035fb26f0c3776abb8176540b827af10af16c 263ad79f78e5bc34088824fb7eb85e7c70a2647b berkincetin <<EMAIL>> 1752754249 +0300	commit: mongodb query fixed
263ad79f78e5bc34088824fb7eb85e7c70a2647b 2f3e96300621272c8d10e3156ff109abd79685f6 berkincetin <<EMAIL>> 1752809721 +0300	commit: async functions update
2f3e96300621272c8d10e3156ff109abd79685f6 9b17de00e5f31889326a49240ec8fb8e352c6a72 berkincetin <<EMAIL>> 1752811284 +0300	commit: sql and mongodb added pooling
9b17de00e5f31889326a49240ec8fb8e352c6a72 d6fc328ef2878b7db582b3ef4546320384e47f90 berkincetin <<EMAIL>> 1752814069 +0300	commit: rate limit added
d6fc328ef2878b7db582b3ef4546320384e47f90 70739d26e824dc635981e8b5dcb0ea91de539ceb berkincetin <<EMAIL>> 1752814156 +0300	commit: rate limit changed
70739d26e824dc635981e8b5dcb0ea91de539ceb 61057507b60ab55f4b7c8e19ad9be84860d2ef6a berkincetin <<EMAIL>> 1752814244 +0300	commit: test file deleted
61057507b60ab55f4b7c8e19ad9be84860d2ef6a 493669a638ccaabfe5c775c163f6de33eefc47a7 berkincetin <<EMAIL>> 1752814444 +0300	commit: test functions removed
493669a638ccaabfe5c775c163f6de33eefc47a7 37006ce64c233a5cd3ec762ce7a99533e2b92735 berkincetin <<EMAIL>> 1752816189 +0300	commit: tool usege changed
